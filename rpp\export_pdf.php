<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();

// First line: turn off output buffering
ob_end_clean();

// Start new output buffering
ob_start();

require_once '../vendor/autoload.php';
require_once '../config/database.php';
require_once '../models/Rpp.php';
require_once '../models/RppKegiatan.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Kelas.php';
require_once '../models/Guru.php';

use Dompdf\Dompdf;
use Dompdf\Options;

try {
    if (!isset($_GET['id'])) {
        throw new Exception("ID RPP tidak ditemukan.");
    }

    $id = $_GET['id'];
    $rpp = new Rpp();
    $rppKegiatan = new RppKegiatan();

    // Ambil data RPP
    $rpp_data = $rpp->getOne($id);
    if (!$rpp_data) {
        throw new Exception("RPP tidak ditemukan.");
    }

    // Ambil data kegiatan pembelajaran
    $stmt_kegiatan = $rppKegiatan->getByRppId($id);
    $kegiatan = [
        'pendahuluan' => [],
        'inti' => [],
        'penutup' => []
    ];

    while ($row = $stmt_kegiatan->fetch(PDO::FETCH_ASSOC)) {
        $kegiatan[$row['jenis_kegiatan']][] = $row;
    }

    // Ambil data tambahan
    $mapel = new MataPelajaran();
    $kelas = new Kelas();
    $guru = new Guru();

    // Set ID dan ambil data
    $mapel->id = $rpp_data['mapel_id'];
    $mapel_data = $mapel->getOne();

    $kelas_data = $kelas->getById($rpp_data['kelas_id']);

    $guru->id = $rpp_data['guru_id'];
    $guru_data = $guru->getOneNew();

    // Cek jika data tidak ditemukan
    if (!$mapel_data || !$kelas_data || !$guru_data) {
        throw new Exception("Data tidak lengkap.");
    }

    // Get application info
    $app_name = "SIHADIR - Sistem Informasi Kehadiran Siswa";
    $app_version = "v2.18.0";

    // Start generating PDF
    $options = new Options();
    $options->set('isHtml5ParserEnabled', true);
    $options->set('isPhpEnabled', true);
    $options->set('defaultFont', 'DejaVu Sans');
    $options->set('isRemoteEnabled', true);

    $dompdf = new Dompdf($options);
    $dompdf->setPaper('A4', 'portrait');

    // Generate HTML content
    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>RPP - ' . htmlspecialchars($mapel_data['nama_mapel']) . '</title>
        <style>
            body {
                font-family: DejaVu Sans, sans-serif;
                line-height: 1.6;
                font-size: 11px;
                margin: 0;
                padding: 20px;
                color: #333;
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 3px solid #007bff;
                padding-bottom: 15px;
            }
            .app-info {
                font-size: 10px;
                color: #666;
                margin-bottom: 5px;
            }
            .main-title {
                font-size: 16px;
                font-weight: bold;
                color: #007bff;
                margin: 10px 0;
            }
            .section {
                margin-bottom: 20px;
                page-break-inside: avoid;
            }
            .section-title {
                font-weight: bold;
                font-size: 12px;
                color: #007bff;
                margin-bottom: 8px;
                padding-bottom: 3px;
                border-bottom: 1px solid #ddd;
            }
            .kegiatan-item {
                margin-left: 15px;
                margin-bottom: 8px;
                padding: 5px;
                background-color: #f8f9fa;
                border-left: 3px solid #007bff;
            }
            .subsection {
                margin-bottom: 15px;
            }
            .subsection strong {
                color: #495057;
                font-size: 11px;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 15px;
            }
            td {
                padding: 6px;
                vertical-align: top;
            }
            .info-table td {
                border-bottom: 1px solid #eee;
            }
            .content {
                max-width: 100%;
            }
            .footer {
                margin-top: 40px;
                text-align: right;
                page-break-inside: avoid;
                border-top: 1px solid #ddd;
                padding-top: 20px;
            }
            .signature-area {
                margin-top: 60px;
            }
        </style>
    </head>
    <body>
        <div class="content">
            <div class="header">
                <div class="app-info">' . htmlspecialchars($app_name) . ' ' . htmlspecialchars($app_version) . '</div>
                <div class="main-title">RENCANA PELAKSANAAN PEMBELAJARAN (RPP)</div>
            </div>
            
            <div class="section">
                <table class="info-table">
                    <tr>
                        <td width="150"><strong>Nama Sekolah</strong></td>
                        <td width="10">:</td>
                        <td>' . htmlspecialchars($rpp_data['nama_sekolah']) . '</td>
                    </tr>
                    <tr>
                        <td><strong>Mata Pelajaran</strong></td>
                        <td>:</td>
                        <td>' . htmlspecialchars($mapel_data['nama_mapel']) . '</td>
                    </tr>
                    <tr>
                        <td><strong>Kelas</strong></td>
                        <td>:</td>
                        <td>' . htmlspecialchars($kelas_data['nama_kelas']) . '</td>
                    </tr>
                    <tr>
                        <td><strong>Semester</strong></td>
                        <td>:</td>
                        <td>' . htmlspecialchars($rpp_data['semester']) . '</td>
                    </tr>
                    <tr>
                        <td><strong>Tahun Ajaran</strong></td>
                        <td>:</td>
                        <td>' . htmlspecialchars($rpp_data['tahun_ajaran']) . '</td>
                    </tr>
                    <tr>
                        <td><strong>Alokasi Waktu</strong></td>
                        <td>:</td>
                        <td>' . htmlspecialchars($rpp_data['alokasi_waktu']) . '</td>
                    </tr>
                    <tr>
                        <td><strong>Guru</strong></td>
                        <td>:</td>
                        <td>' . htmlspecialchars($guru_data['nama_lengkap']) . '</td>
                    </tr>
                </table>
            </div>

            <div class="section">
                <div class="section-title">A. Tujuan Pembelajaran</div>
                ' . nl2br(htmlspecialchars($rpp_data['tujuan_pembelajaran'])) . '
            </div>

            <div class="section">
                <div class="section-title">B. Kompetensi Dasar</div>
                ' . nl2br(htmlspecialchars($rpp_data['kompetensi_dasar'])) . '
            </div>

            <div class="section">
                <div class="section-title">C. Indikator Pencapaian Kompetensi</div>
                ' . nl2br(htmlspecialchars($rpp_data['indikator_pencapaian'])) . '
            </div>

            <div class="section">
                <div class="section-title">D. Materi Pembelajaran</div>
                ' . nl2br(htmlspecialchars($rpp_data['materi_pembelajaran'])) . '
            </div>

            <div class="section">
                <div class="section-title">E. Kegiatan Pembelajaran</div>
                
                <div class="subsection">
                    <strong>1. Pendahuluan</strong><br>';
                    if (isset($kegiatan['pendahuluan'])) {
                        foreach ($kegiatan['pendahuluan'] as $item) {
                            $html .= '<div class="kegiatan-item">' . nl2br(htmlspecialchars($item['deskripsi'])) . '</div>';
                        }
                    }
    
    $html .= '
                </div>
                
                <div class="subsection">
                    <strong>2. Kegiatan Inti</strong><br>';
                    if (isset($kegiatan['inti'])) {
                        foreach ($kegiatan['inti'] as $item) {
                            $html .= '<div class="kegiatan-item">' . nl2br(htmlspecialchars($item['deskripsi'])) . '</div>';
                        }
                    }
    
    $html .= '
                </div>
                
                <div class="subsection">
                    <strong>3. Penutup</strong><br>';
                    if (isset($kegiatan['penutup'])) {
                        foreach ($kegiatan['penutup'] as $item) {
                            $html .= '<div class="kegiatan-item">' . nl2br(htmlspecialchars($item['deskripsi'])) . '</div>';
                        }
                    }
    
    $html .= '
                </div>
            </div>

            <div class="section">
                <div class="section-title">F. Metode Pembelajaran</div>
                ' . nl2br(htmlspecialchars($rpp_data['metode_pembelajaran'])) . '
            </div>

            <div class="section">
                <div class="section-title">G. Media Pembelajaran</div>
                ' . nl2br(htmlspecialchars($rpp_data['media_pembelajaran'])) . '
            </div>

            <div class="section">
                <div class="section-title">H. Sumber Belajar</div>
                ' . nl2br(htmlspecialchars($rpp_data['sumber_belajar'])) . '
            </div>

            <div class="section">
                <div class="section-title">I. Penilaian</div>
                ' . nl2br(htmlspecialchars($rpp_data['penilaian'])) . '
            </div>

            <div class="footer">
                <div class="signature-area">
                    <p>
                        ' . htmlspecialchars($rpp_data['nama_sekolah']) . ', ' . date('d F Y') . '<br><br>
                        <strong>Guru Mata Pelajaran</strong><br><br><br><br>
                        <strong>' . htmlspecialchars($guru_data['nama_lengkap']) . '</strong><br>
                        NIP: ' . htmlspecialchars($guru_data['nip'] ?? '-') . '
                    </p>
                </div>
            </div>
        </div>
    </body>
    </html>';

    $dompdf->loadHtml($html);
    $dompdf->render();

    // Output PDF
    $filename = "RPP_" . $mapel_data['nama_mapel'] . "_" . date('Ymd') . ".pdf";
    $dompdf->stream($filename, array("Attachment" => true));
    exit();

} catch (Exception $e) {
    error_log('PDF Export Error: ' . $e->getMessage());
    $_SESSION['error'] = "Maaf, terjadi kesalahan saat mengekspor file PDF: " . $e->getMessage();
    header("Location: index.php");
    exit();
}